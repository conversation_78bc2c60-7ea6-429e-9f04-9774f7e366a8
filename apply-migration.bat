@echo off
echo Applying location coordinates migration...

echo.
echo This will add the following columns to the properties table:
echo - latitude (DECIMAL)
echo - longitude (DECIMAL) 
echo - formatted_address (TEXT)
echo - neighborhood (TEXT)
echo - city (TEXT)
echo.

echo You need to apply this migration to your Supabase database.
echo.
echo Option 1: Using Supabase Dashboard
echo - Go to https://app.supabase.com/project/pikolpdkdvwtnphdylyp/sql
echo - Copy and paste the contents of add-location-coordinates-migration.sql
echo - Click "Run"
echo.
echo Option 2: Using Supabase CLI (if installed)
echo - Run: supabase db reset
echo - Or run: psql -h YOUR_DB_HOST -p 5432 -U postgres -d postgres -f add-location-coordinates-migration.sql
echo.

echo Once migration is applied, the app will support:
echo - Interactive embedded maps showing exact property locations
echo - Location suggestions with coordinates
echo - GPS location detection for landlords
echo - Enhanced search by location
echo.

pause
