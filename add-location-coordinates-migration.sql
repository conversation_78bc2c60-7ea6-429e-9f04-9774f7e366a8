-- Add coordinates and structured location fields to properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS formatted_address TEXT,
ADD COLUMN IF NOT EXISTS neighborhood TEXT,
ADD COLUMN IF NOT EXISTS city TEXT;

-- Create index for location-based queries
CREATE INDEX IF NOT EXISTS idx_properties_coordinates ON public.properties(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_properties_location_search ON public.properties USING GIN (to_tsvector('english', location || ' ' || COALESCE(neighborhood, '') || ' ' || COALESCE(city, '') || ' ' || county));
