-- Multi-Unit Property Enhancement Migration
-- This migration adds support for multi-unit properties

-- Step 1: Add columns to existing properties table
ALTER TABLE properties 
ADD COLUMN IF NOT EXISTS is_multi_unit BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS building_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS total_units INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS property_type VARCHAR(50) DEFAULT 'single_unit';

-- Step 2: Create property_units table
CREATE TABLE IF NOT EXISTS property_units (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    unit_name VARCHAR(100), -- e.g., "Single Room A1", "Studio B2"
    room_type VARCHAR(50) NOT NULL, -- 'single_room', 'bedsitter', 'studio', '1_bedroom', etc.
    bedrooms INTEGER NOT NULL,
    bathrooms INTEGER NOT NULL,
    area DECIMAL,
    rent DECIMAL NOT NULL,
    deposit DECIMAL,
    is_available BOOLEAN DEFAULT TRUE,
    unit_amenities TEXT[],
    unit_description TEXT,
    floor_number INTEGER,
    unit_number VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Step 3: Create unit_images table
CREATE TABLE IF NOT EXISTS unit_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Step 4: Create unit_inquiries table
CREATE TABLE IF NOT EXISTS unit_inquiries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    inquiry_type VARCHAR(20) DEFAULT 'viewing', -- 'viewing', 'booking', 'application'
    message TEXT,
    contact_phone VARCHAR(20),
    preferred_date DATE,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed'
    created_at TIMESTAMP DEFAULT NOW()
);

-- Step 5: Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_property_units_property_id ON property_units(property_id);
CREATE INDEX IF NOT EXISTS idx_property_units_room_type ON property_units(room_type);
CREATE INDEX IF NOT EXISTS idx_property_units_is_available ON property_units(is_available);
CREATE INDEX IF NOT EXISTS idx_property_units_rent ON property_units(rent);
CREATE INDEX IF NOT EXISTS idx_unit_images_unit_id ON unit_images(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_inquiries_unit_id ON unit_inquiries(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_inquiries_user_id ON unit_inquiries(user_id);

-- Step 6: Enable Row Level Security
ALTER TABLE property_units ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_inquiries ENABLE ROW LEVEL SECURITY;

-- Step 7: Create RLS policies
-- Property units can be viewed by anyone, but only edited by property owner
CREATE POLICY "Anyone can view property units"
ON property_units FOR SELECT
USING (true);

CREATE POLICY "Property owners can manage their units"
ON property_units FOR ALL
USING (
    property_id IN (
        SELECT id FROM properties WHERE user_id = auth.uid()
    )
);

-- Unit images policies
CREATE POLICY "Anyone can view unit images"
ON unit_images FOR SELECT
USING (true);

CREATE POLICY "Property owners can manage unit images"
ON unit_images FOR ALL
USING (
    unit_id IN (
        SELECT pu.id FROM property_units pu
        JOIN properties p ON pu.property_id = p.id
        WHERE p.user_id = auth.uid()
    )
);

-- Unit inquiries policies
CREATE POLICY "Users can view their own inquiries"
ON unit_inquiries FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can create inquiries"
ON unit_inquiries FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Property owners can view inquiries for their units"
ON unit_inquiries FOR SELECT
USING (
    unit_id IN (
        SELECT pu.id FROM property_units pu
        JOIN properties p ON pu.property_id = p.id
        WHERE p.user_id = auth.uid()
    )
);

CREATE POLICY "Property owners can update inquiry status"
ON unit_inquiries FOR UPDATE
USING (
    unit_id IN (
        SELECT pu.id FROM property_units pu
        JOIN properties p ON pu.property_id = p.id
        WHERE p.user_id = auth.uid()
    )
);
