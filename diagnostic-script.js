#!/usr/bin/env node

/**
 * Bo<PERSON> Finder Diagnostic Script
 * This script checks for common issues that cause blank pages in React/Vite apps
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏠 Boma Finder System Diagnostic Script\n');
console.log('========================================\n');

const projectRoot = process.cwd();
const issues = [];
const recommendations = [];

function checkFile(filePath, description) {
  const fullPath = path.join(projectRoot, filePath);
  const exists = fs.existsSync(fullPath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${exists ? 'Found' : 'Missing'}`);
  if (!exists) {
    issues.push(`Missing ${description}`);
  }
  return exists;
}

function checkFileContent(filePath, searchTerm, description) {
  const fullPath = path.join(projectRoot, filePath);
  if (!fs.existsSync(fullPath)) return false;
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const found = content.includes(searchTerm);
  console.log(`${found ? '✅' : '❌'} ${description}: ${found ? 'Found' : 'Not found'}`);
  if (!found) {
    issues.push(`Missing ${description} in ${filePath}`);
  }
  return found;
}

function runCommand(command, description) {
  try {
    const output = execSync(command, { encoding: 'utf8', cwd: projectRoot });
    console.log(`✅ ${description}: Success`);
    return output;
  } catch (error) {
    console.log(`❌ ${description}: Failed`);
    console.log(`   Error: ${error.message}`);
    issues.push(`${description} failed: ${error.message}`);
    return null;
  }
}

console.log('1. Checking Core Files\n');
console.log('======================');

// Check essential files
checkFile('package.json', 'Package.json');
checkFile('index.html', 'index.html');
checkFile('src/main.tsx', 'main.tsx entry point');
checkFile('src/App.tsx', 'App.tsx component');
checkFile('.env', 'Environment file');

console.log('\n2. Checking Package.json Configuration\n');
console.log('======================================');

// Check package.json scripts and dependencies
if (checkFile('package.json', 'package.json')) {
  const packageJson = JSON.parse(fs.readFileSync(path.join(projectRoot, 'package.json'), 'utf8'));
  
  // Check scripts
  if (packageJson.scripts && packageJson.scripts.dev) {
    console.log('✅ Dev script found:', packageJson.scripts.dev);
  } else {
    console.log('❌ Dev script missing in package.json');
    issues.push('Missing dev script in package.json');
  }
  
  // Check essential dependencies
  const essentialDeps = ['react', 'react-dom', 'vite'];
  essentialDeps.forEach(dep => {
    const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
    const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
    const found = hasInDeps || hasInDevDeps;
    console.log(`${found ? '✅' : '❌'} ${dep}: ${found ? 'Found' : 'Missing'}`);
    if (!found) {
      issues.push(`Missing dependency: ${dep}`);
    }
  });
}

console.log('\n3. Checking HTML Template\n');
console.log('=========================');

// Check index.html
if (checkFile('index.html', 'index.html')) {
  checkFileContent('index.html', '<div id="root">', 'Root div element');
  checkFileContent('index.html', 'src="/src/main.tsx"', 'Main.tsx script reference');
}

console.log('\n4. Checking Environment Variables\n');
console.log('=================================');

// Check .env file
if (checkFile('.env', '.env file')) {
  checkFileContent('.env', 'VITE_SUPABASE_URL', 'Supabase URL');
  checkFileContent('.env', 'VITE_SUPABASE_ANON_KEY', 'Supabase Anon Key');
  
  // Check if .env values are not placeholder values
  const envContent = fs.readFileSync(path.join(projectRoot, '.env'), 'utf8');
  if (envContent.includes('your_supabase_url_here') || envContent.includes('your_supabase_anon_key_here')) {
    console.log('❌ Environment variables contain placeholder values');
    issues.push('Environment variables contain placeholder values');
    recommendations.push('Update .env file with actual Supabase credentials');
  }
}

console.log('\n5. Checking TypeScript Configuration\n');
console.log('====================================');

// Check TypeScript files
checkFile('tsconfig.json', 'TypeScript config');
checkFile('vite.config.ts', 'Vite config');

console.log('\n6. Checking Dependencies Installation\n');
console.log('====================================');

// Check if node_modules exists
if (checkFile('node_modules', 'Node modules directory')) {
  console.log('✅ Dependencies appear to be installed');
} else {
  console.log('❌ Node modules not found');
  issues.push('Dependencies not installed');
  recommendations.push('Run: npm install');
}

console.log('\n7. Checking Build System\n');
console.log('========================');

// Try to check if TypeScript compiles
console.log('Checking TypeScript compilation...');
runCommand('npx tsc --noEmit', 'TypeScript compilation check');

console.log('\n8. Testing React App Structure\n');
console.log('==============================');

// Check main.tsx structure
if (checkFile('src/main.tsx', 'main.tsx')) {
  checkFileContent('src/main.tsx', 'createRoot', 'React 18 createRoot usage');
  checkFileContent('src/main.tsx', 'document.getElementById("root")', 'Root element reference');
  checkFileContent('src/main.tsx', '<App />', 'App component import');
}

// Check App.tsx structure
if (checkFile('src/App.tsx', 'App.tsx')) {
  checkFileContent('src/App.tsx', 'export default', 'Default export');
  checkFileContent('src/App.tsx', 'BrowserRouter', 'Router configuration');
}

console.log('\n9. Checking for Common Issues\n');
console.log('=============================');

// Check for common React issues
const commonIssueChecks = [
  {
    file: 'src/integrations/supabase/client.ts',
    check: 'import.meta.env.VITE_SUPABASE_URL',
    description: 'Supabase environment variable usage'
  },
  {
    file: 'src/hooks/useAuth.ts',
    check: 'createContext',
    description: 'Auth context creation'
  }
];

commonIssueChecks.forEach(({ file, check, description }) => {
  if (fs.existsSync(path.join(projectRoot, file))) {
    checkFileContent(file, check, description);
  }
});

console.log('\n10. Network and Port Checks\n');
console.log('===========================');

// Check if port 8080 is available or in use
try {
  const netstatOutput = execSync('netstat -an | findstr :8080', { encoding: 'utf8' });
  if (netstatOutput) {
    console.log('✅ Port 8080 is in use (server might be running)');
    console.log('   Active connections on port 8080:');
    console.log('  ', netstatOutput.trim());
  }
} catch (error) {
  console.log('❌ Port 8080 is not in use (server not running)');
  issues.push('Development server not running on port 8080');
  recommendations.push('Run: npm run dev');
}

console.log('\n' + '='.repeat(50));
console.log('📊 DIAGNOSTIC SUMMARY');
console.log('='.repeat(50));

if (issues.length === 0) {
  console.log('✅ No critical issues found!');
  console.log('\nIf you\'re still seeing a blank page, try:');
  console.log('1. Check browser console for JavaScript errors');
  console.log('2. Clear browser cache and localStorage');
  console.log('3. Try incognito/private browsing mode');
  console.log('4. Restart the development server');
} else {
  console.log(`❌ Found ${issues.length} issues:\n`);
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
  
  if (recommendations.length > 0) {
    console.log('\n🔧 RECOMMENDED ACTIONS:\n');
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Fix the issues listed above');
  console.log('2. Run: npm install');
  console.log('3. Run: npm run dev');
  console.log('4. Check browser console at http://localhost:8080');
  console.log('5. Open system-diagnostic.html in browser for detailed analysis');
}

console.log('\n🔍 For real-time debugging:');
console.log('Open system-diagnostic.html in your browser');
console.log('Visit http://localhost:8080 and check browser console (F12)');

process.exit(issues.length > 0 ? 1 : 0);
