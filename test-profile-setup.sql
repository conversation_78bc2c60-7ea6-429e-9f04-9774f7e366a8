-- Test profile functionality
-- Run this after the fix migration to verify everything works

-- Check if profiles table has correct structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check existing profiles
SELECT id, full_name, phone, created_at 
FROM public.profiles 
LIMIT 5;

-- Test trigger function exists
SELECT proname, prosrc 
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- Check trigger exists
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';
