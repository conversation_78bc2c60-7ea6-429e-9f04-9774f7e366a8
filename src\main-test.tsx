import React from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

// Minimal test component
const TestApp = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial' }}>
      <h1 style={{ color: 'green' }}>✅ React App is Working!</h1>
      <p>If you can see this, React is loading correctly.</p>
      <p>Current time: {new Date().toLocaleString()}</p>
      <button onClick={() => alert('JavaScript is working!')}>
        Test JavaScript
      </button>
    </div>
  )
}

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error('Root element not found');
}

const root = createRoot(rootElement);
root.render(<TestApp />);
