-- Add units column to properties table
-- This migration adds a units column to store unit data as JSONB

-- Add the units column to the properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS units JSONB DEFAULT '[]'::jsonb;

-- Add a comment to document the column
COMMENT ON COLUMN public.properties.units IS 'JSON array containing unit data for multi-unit properties';

-- Optional: Add an index on the units column for better query performance
CREATE INDEX IF NOT EXISTS idx_properties_units ON public.properties USING GIN (units);

-- Update existing properties to have an empty units array if they don't have one
UPDATE public.properties 
SET units = '[]'::jsonb 
WHERE units IS NULL;
