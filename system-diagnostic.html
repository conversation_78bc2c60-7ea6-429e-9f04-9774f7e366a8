<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boma Finder System Diagnostic</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
        }
        .debug-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
        }
        .error { color: #ff6b6b; font-weight: bold; }
        .success { color: #51cf66; font-weight: bold; }
        .warning { color: #ffd43b; font-weight: bold; }
        .info { color: #74c0fc; }
        button {
            background: #4c6ef5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #3b5bdb;
            transform: translateY(-2px);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .result-box {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        h1 { text-align: center; color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        h2 { color: #74c0fc; border-bottom: 2px solid #74c0fc; padding-bottom: 10px; }
        pre {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #4c6ef5;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-green { background-color: #51cf66; }
        .status-red { background-color: #ff6b6b; }
        .status-yellow { background-color: #ffd43b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Boma Finder System Diagnostic Dashboard</h1>
        
        <div class="debug-section">
            <h2>🔍 Quick System Check</h2>
            <button onclick="runFullDiagnostic()">Run Full System Diagnostic</button>
            <button onclick="testReactApp()">Test React App at localhost:8080</button>
            <button onclick="clearAllResults()">Clear All Results</button>
            <div id="quick-results" class="result-box"></div>
        </div>

        <div class="grid">
            <div class="debug-section">
                <h2>⚙️ Environment Check</h2>
                <button onclick="checkEnvironment()">Check Environment</button>
                <div id="env-results" class="result-box"></div>
            </div>

            <div class="debug-section">
                <h2>🌐 Network Tests</h2>
                <button onclick="testNetworking()">Test All Network Endpoints</button>
                <div id="network-results" class="result-box"></div>
            </div>
        </div>

        <div class="grid">
            <div class="debug-section">
                <h2>📦 Dependencies Check</h2>
                <button onclick="checkDependencies()">Check Package Dependencies</button>
                <div id="deps-results" class="result-box"></div>
            </div>

            <div class="debug-section">
                <h2>🐛 Error Monitor</h2>
                <button onclick="startErrorCapture()">Start Error Capture</button>
                <button onclick="stopErrorCapture()">Stop Error Capture</button>
                <div id="error-results" class="result-box"></div>
            </div>
        </div>

        <div class="debug-section">
            <h2>🏗️ Build & Dev Server Analysis</h2>
            <button onclick="analyzeBuildSystem()">Analyze Build System</button>
            <button onclick="testDevServerPorts()">Test All Server Ports</button>
            <div id="build-results" class="result-box"></div>
        </div>

        <div class="debug-section">
            <h2>📊 Real-time Diagnosis Results</h2>
            <div id="comprehensive-results" class="result-box">
                <p class="info">Click "Run Full System Diagnostic" to start comprehensive analysis...</p>
            </div>
        </div>
    </div>

    <script>
        let errorCapture = false;
        let capturedErrors = [];
        let diagnosticResults = {};

        function addResult(section, status, message, details = '') {
            const element = document.getElementById(section);
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = status === 'success' ? 'status-green' : status === 'error' ? 'status-red' : 'status-yellow';
            const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️';
            
            const resultHtml = `
                <div style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                    <span class="status-indicator ${statusClass}"></span>
                    <span class="${status}">[${timestamp}] ${statusIcon} ${message}</span>
                    ${details ? `<div style="margin-top: 5px; font-size: 0.9em; opacity: 0.8;">${details}</div>` : ''}
                </div>
            `;
            
            element.innerHTML += resultHtml;
            diagnosticResults[section] = diagnosticResults[section] || [];
            diagnosticResults[section].push({ timestamp, status, message, details });
        }

        function clearSection(section) {
            document.getElementById(section).innerHTML = '';
            diagnosticResults[section] = [];
        }

        function clearAllResults() {
            ['quick-results', 'env-results', 'network-results', 'deps-results', 'error-results', 'build-results', 'comprehensive-results'].forEach(clearSection);
            capturedErrors = [];
        }

        async function runFullDiagnostic() {
            clearAllResults();
            addResult('comprehensive-results', 'info', 'Starting comprehensive system diagnostic...');
            
            // Run all checks in sequence
            await checkEnvironment();
            await testNetworking();
            await checkDependencies();
            await analyzeBuildSystem();
            await testDevServerPorts();
            
            // Generate summary
            const summary = generateDiagnosticSummary();
            addResult('comprehensive-results', 'info', 'Diagnostic complete! Summary:', summary);
        }

        async function checkEnvironment() {
            addResult('env-results', 'info', 'Checking environment configuration...');
            
            // Check if we can access the current page
            try {
                const currentUrl = window.location.href;
                addResult('env-results', 'success', `Current page accessible: ${currentUrl}`);
            } catch (e) {
                addResult('env-results', 'error', 'Cannot access current page', e.message);
            }

            // Test local storage
            try {
                localStorage.setItem('diagnostic-test', 'test-value');
                const testValue = localStorage.getItem('diagnostic-test');
                localStorage.removeItem('diagnostic-test');
                addResult('env-results', 'success', 'Local Storage working');
            } catch (e) {
                addResult('env-results', 'error', 'Local Storage failed', e.message);
            }

            // Test console
            try {
                console.log('Diagnostic console test');
                addResult('env-results', 'success', 'Console API working');
            } catch (e) {
                addResult('env-results', 'error', 'Console API failed', e.message);
            }

            // Check if we're in development mode
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            addResult('env-results', isDev ? 'success' : 'warning', `Development mode: ${isDev}`);
        }

        async function testNetworking() {
            addResult('network-results', 'info', 'Testing network connectivity...');
            
            const testUrls = [
                { url: 'http://localhost:8080/', description: 'React App (localhost)' },
                { url: 'http://*************:8080/', description: 'React App (Network IP 1)' },
                { url: 'http://**************:8080/', description: 'React App (Network IP 2)' },
                { url: 'https://jsonplaceholder.typicode.com/posts/1', description: 'External API Test' },
                { url: 'https://pikolpdkdvwtnphdylyp.supabase.co/rest/v1/', description: 'Supabase API' }
            ];

            for (const test of testUrls) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
                    
                    const response = await fetch(test.url, { 
                        signal: controller.signal,
                        mode: 'no-cors' // Avoid CORS issues for diagnostic
                    });
                    clearTimeout(timeoutId);
                    
                    addResult('network-results', 'success', `${test.description} accessible`);
                } catch (e) {
                    if (e.name === 'AbortError') {
                        addResult('network-results', 'error', `${test.description} timeout`, 'Request took longer than 5 seconds');
                    } else {
                        addResult('network-results', 'error', `${test.description} failed`, e.message);
                    }
                }
            }
        }

        async function checkDependencies() {
            addResult('deps-results', 'info', 'Checking JavaScript dependencies...');
            
            const dependencies = [
                { name: 'React', check: () => typeof React !== 'undefined' },
                { name: 'ReactDOM', check: () => typeof ReactDOM !== 'undefined' },
                { name: 'Fetch API', check: () => typeof fetch !== 'undefined' },
                { name: 'Promise API', check: () => typeof Promise !== 'undefined' },
                { name: 'ES6 Support', check: () => { try { eval('const test = () => {}; return true'); return true; } catch { return false; } } },
                { name: 'Local Storage', check: () => typeof localStorage !== 'undefined' },
                { name: 'Service Worker', check: () => 'serviceWorker' in navigator }
            ];

            dependencies.forEach(dep => {
                try {
                    const available = dep.check();
                    addResult('deps-results', available ? 'success' : 'error', `${dep.name}: ${available ? 'Available' : 'Not Available'}`);
                } catch (e) {
                    addResult('deps-results', 'error', `${dep.name}: Error checking`, e.message);
                }
            });
        }

        async function analyzeBuildSystem() {
            addResult('build-results', 'info', 'Analyzing build system and page structure...');
            
            // Check if we can find React root element
            const rootElement = document.getElementById('root');
            if (rootElement) {
                const hasContent = rootElement.innerHTML.trim().length > 0;
                addResult('build-results', hasContent ? 'success' : 'error', 
                    `React root element found, has content: ${hasContent}`,
                    hasContent ? `Content: ${rootElement.innerHTML.substring(0, 100)}...` : 'Root element is empty - this might be the cause of blank page');
            } else {
                addResult('build-results', 'error', 'React root element (#root) not found', 'This is a critical issue that will cause blank page');
            }

            // Check for script tags
            const scripts = document.querySelectorAll('script');
            addResult('build-results', 'info', `Found ${scripts.length} script tags`);
            
            scripts.forEach((script, index) => {
                if (script.src) {
                    const isViteScript = script.src.includes('main.tsx') || script.src.includes('@vite');
                    addResult('build-results', isViteScript ? 'success' : 'info', 
                        `Script ${index + 1}: ${script.src.substring(script.src.lastIndexOf('/') + 1)}`);
                }
            });

            // Check for CSS links
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            addResult('build-results', 'info', `Found ${stylesheets.length} stylesheet links`);
        }

        async function testDevServerPorts() {
            addResult('build-results', 'info', 'Testing development server ports...');
            
            const ports = [8080, 3000, 5173, 4173]; // Common Vite/React ports
            const hosts = ['localhost', '127.0.0.1'];
            
            for (const host of hosts) {
                for (const port of ports) {
                    try {
                        const url = `http://${host}:${port}/`;
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 3000);
                        
                        const response = await fetch(url, { 
                            signal: controller.signal,
                            mode: 'no-cors'
                        });
                        clearTimeout(timeoutId);
                        
                        addResult('build-results', 'success', `Server found at ${host}:${port}`);
                    } catch (e) {
                        // Only log if it's the expected port
                        if (port === 8080) {
                            addResult('build-results', 'warning', `No server at ${host}:${port}`, e.message);
                        }
                    }
                }
            }
        }

        async function testReactApp() {
            addResult('quick-results', 'info', 'Testing React app directly...');
            
            try {
                // Try to open the React app in a new window
                const testWindow = window.open('http://localhost:8080/', 'react-test', 'width=800,height=600');
                
                setTimeout(() => {
                    try {
                        if (testWindow && !testWindow.closed) {
                            const hasContent = testWindow.document.body.innerHTML.trim().length > 0;
                            addResult('quick-results', hasContent ? 'success' : 'error', 
                                `React app window opened, has content: ${hasContent}`);
                            
                            if (!hasContent) {
                                addResult('quick-results', 'error', 'React app shows blank page - this confirms the issue');
                            }
                            
                            testWindow.close();
                        } else {
                            addResult('quick-results', 'error', 'Could not open React app window');
                        }
                    } catch (e) {
                        addResult('quick-results', 'warning', 'Could not inspect React app window', 'This might be due to CORS or security restrictions');
                    }
                }, 2000);
                
            } catch (e) {
                addResult('quick-results', 'error', 'Failed to test React app', e.message);
            }
        }

        function startErrorCapture() {
            if (errorCapture) return;
            
            errorCapture = true;
            capturedErrors = [];
            addResult('error-results', 'info', 'Started error capture...');
            
            // Capture console errors
            const originalError = console.error;
            console.error = function(...args) {
                capturedErrors.push({
                    type: 'console.error',
                    message: args.join(' '),
                    timestamp: new Date().toISOString()
                });
                addResult('error-results', 'error', 'Console Error', args.join(' '));
                originalError.apply(console, args);
            };

            // Capture window errors
            window.addEventListener('error', function(e) {
                capturedErrors.push({
                    type: 'window.error',
                    message: e.message,
                    filename: e.filename,
                    lineno: e.lineno,
                    timestamp: new Date().toISOString()
                });
                addResult('error-results', 'error', 'Window Error', `${e.message} at ${e.filename}:${e.lineno}`);
            });

            // Capture unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                capturedErrors.push({
                    type: 'unhandledrejection',
                    message: e.reason?.message || e.reason,
                    timestamp: new Date().toISOString()
                });
                addResult('error-results', 'error', 'Unhandled Promise Rejection', e.reason?.message || e.reason);
            });
        }

        function stopErrorCapture() {
            errorCapture = false;
            addResult('error-results', 'info', `Error capture stopped. Captured ${capturedErrors.length} errors.`);
        }

        function generateDiagnosticSummary() {
            let summary = '<h3>🎯 Diagnostic Summary</h3>';
            let totalIssues = 0;
            let criticalIssues = [];
            
            Object.keys(diagnosticResults).forEach(section => {
                const results = diagnosticResults[section] || [];
                const errors = results.filter(r => r.status === 'error');
                const warnings = results.filter(r => r.status === 'warning');
                
                totalIssues += errors.length + warnings.length;
                
                errors.forEach(error => {
                    criticalIssues.push(`❌ ${error.message}`);
                });
            });
            
            summary += `<p><strong>Total Issues Found:</strong> ${totalIssues}</p>`;
            
            if (criticalIssues.length > 0) {
                summary += '<p><strong>Critical Issues:</strong></p><ul>';
                criticalIssues.forEach(issue => {
                    summary += `<li>${issue}</li>`;
                });
                summary += '</ul>';
                
                summary += '<h4>🔧 Recommended Actions:</h4><ul>';
                summary += '<li>Check if .env file has correct Supabase credentials</li>';
                summary += '<li>Verify npm/yarn dependencies are installed correctly</li>';
                summary += '<li>Check browser console for JavaScript errors</li>';
                summary += '<li>Ensure Vite dev server is running on port 8080</li>';
                summary += '<li>Try running: npm install && npm run dev</li>';
                summary += '</ul>';
            } else {
                summary += '<p class="success">✅ No critical issues found! The blank page might be due to a different cause.</p>';
            }
            
            return summary;
        }

        // Auto-start error capture when page loads
        window.onload = function() {
            startErrorCapture();
            setTimeout(() => {
                addResult('quick-results', 'info', 'Diagnostic tool loaded successfully');
                addResult('quick-results', 'info', 'Click "Run Full System Diagnostic" to analyze the blank page issue');
            }, 500);
        };
    </script>
</body>
</html>
