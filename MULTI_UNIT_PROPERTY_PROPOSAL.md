# Multi-Unit Property Management System Enhancement

## Overview
This proposal outlines enhancements to the current BomaFinder system to support landlords with multiple room types and units within a single property.

## Current System Limitations
1. **Single Unit Focus**: Current system treats each property as a single unit
2. **Fixed Room Configuration**: One bedroom count, one bathroom count per property
3. **Single Rent Amount**: Cannot handle different rent amounts for different room types
4. **Separate Listings Required**: Landlords must create multiple listings for same building

## Proposed Database Schema Changes

### 1. Enhanced Properties Table
```sql
-- Add new columns to properties table
ALTER TABLE properties ADD COLUMN is_multi_unit BOOLEAN DEFAULT FALSE;
ALTER TABLE properties ADD COLUMN building_name VARCHAR(255);
ALTER TABLE properties ADD COLUMN total_units INTEGER DEFAULT 1;
ALTER TABLE properties ADD COLUMN property_type VARCHAR(50) DEFAULT 'single_unit'; -- 'single_unit', 'multi_unit', 'complex'
```

### 2. New Property Units Table
```sql
CREATE TABLE property_units (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    unit_name VARCHAR(100), -- e.g., "Single Room A1", "Studio B2"
    room_type VARCHAR(50) NOT NULL, -- 'single_room', 'bedsitter', 'studio', '1_bedroom', etc.
    bedrooms INTEGER NOT NULL,
    bathrooms INTEGER NOT NULL,
    area DECIMAL,
    rent DECIMAL NOT NULL,
    deposit DECIMAL,
    is_available BOOLEAN DEFAULT TRUE,
    unit_amenities TEXT[],
    unit_description TEXT,
    floor_number INTEGER,
    unit_number VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Unit Images Table
```sql
CREATE TABLE unit_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Unit Bookings/Inquiries Table
```sql
CREATE TABLE unit_inquiries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    inquiry_type VARCHAR(20) DEFAULT 'viewing', -- 'viewing', 'booking', 'application'
    message TEXT,
    contact_phone VARCHAR(20),
    preferred_date DATE,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed'
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Frontend Component Enhancements

### 1. Property Type Selection
- Radio buttons for "Single Unit" vs "Multi-Unit Property"
- When "Multi-Unit" is selected, show building-level information fields

### 2. Unit Management Interface
- Add/Remove units dynamically
- Each unit has its own configuration:
  - Room type (dropdown)
  - Bedrooms/Bathrooms
  - Rent amount
  - Availability status
  - Unit-specific amenities
  - Unit-specific images

### 3. Enhanced Property Detail View
- Show building information at top
- Tabbed interface for different unit types
- Unit comparison table
- Individual unit detail cards

## Benefits for Landlords

### 1. Simplified Management
- **Single Property Listing**: One listing for entire building/complex
- **Centralized Updates**: Update building-level information once
- **Unit-Level Control**: Manage availability, pricing per unit type

### 2. Better Organization
- **Clear Unit Tracking**: Know exactly which units are available
- **Type-Based Pricing**: Different rates for different room types
- **Bulk Operations**: Mark multiple units as available/unavailable

### 3. Enhanced Analytics
- **Occupancy Rates**: Track by unit type
- **Revenue Tracking**: See income per unit type
- **Popular Unit Types**: Understand demand patterns

## Benefits for Tenants

### 1. Better Search Experience
- **Compare Options**: See all available units in one property
- **Filter by Unit Type**: Find specific room type preferences
- **Price Comparison**: Compare similar units in same location

### 2. More Information
- **Unit-Specific Details**: Each unit type clearly described
- **Accurate Expectations**: Know exactly what you're viewing
- **Building Amenities**: Understand shared facilities

## Implementation Phases

### Phase 1: Database Schema Updates
- Create new tables
- Add migration scripts
- Update existing property types

### Phase 2: Backend API Updates
- New endpoints for unit management
- Enhanced property queries
- Unit-based search functionality

### Phase 3: Frontend Components
- Multi-unit property creation form
- Enhanced property detail view
- Unit management dashboard

### Phase 4: Advanced Features
- Unit booking system
- Tenant application tracking
- Landlord analytics dashboard

## Example Use Cases

### Case 1: Apartment Building
- **Building**: "Sunrise Apartments, Westlands"
- **Units**: 
  - 10x Single Rooms @ KES 15,000/month
  - 8x Bedsitters @ KES 25,000/month
  - 5x 1-Bedroom @ KES 35,000/month

### Case 2: Mixed-Use Complex
- **Complex**: "Green Valley Residences, Kilimani"
- **Units**:
  - 15x Studio Apartments @ KES 30,000/month
  - 12x 1-Bedroom @ KES 45,000/month
  - 8x 2-Bedroom @ KES 65,000/month
  - 3x 3-Bedroom Penthouses @ KES 120,000/month

## Migration Strategy

### For Existing Properties
1. **Automatic Conversion**: Existing single properties remain as single-unit
2. **Opt-in Upgrade**: Landlords can choose to convert to multi-unit
3. **Data Preservation**: All existing data maintained during transition

### For New Properties
1. **Property Type Selection**: Choose single or multi-unit during creation
2. **Progressive Enhancement**: Start simple, add complexity as needed
3. **Template System**: Pre-defined templates for common building types

## Technical Considerations

### 1. Database Performance
- Proper indexing on unit searches
- Efficient joins for unit-property relationships
- Caching strategies for popular searches

### 2. API Design
- RESTful endpoints for unit management
- Bulk operations for efficiency
- Real-time updates for availability

### 3. User Experience
- Progressive disclosure of complexity
- Intuitive unit management interface
- Mobile-responsive design

## Conclusion

This multi-unit property system will significantly improve the experience for landlords managing multiple room types while providing tenants with better search and comparison capabilities. The phased implementation approach ensures smooth transition from the current system.
