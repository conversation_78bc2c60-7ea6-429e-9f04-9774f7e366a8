# Profile Creation Error Fix

## Problem
You're experiencing a "Error creating profile" message when navigating to `/profile`. The error is related to:
1. Malformed Supabase queries with conflicting parameters (406/400 HTTP errors)
2. Missing phone field in the database trigger function
3. Race conditions during profile creation

## Solution

### Step 1: Update Database
Run the migration script to fix the database issues:

```sql
-- Execute this in your Supabase SQL editor
-- File: fix-profile-migration.sql
```

This script will:
- Ensure the phone field exists in the profiles table
- Update the trigger function to include phone data
- Fix existing profiles that might have missing phone fields

### Step 2: Clear Browser Cache
The 406/400 errors might be caused by cached requests. Clear your browser cache or:
- Open DevTools (F12)
- Right-click the refresh button
- Select "Empty Cache and Hard Reload"

### Step 3: Test the Application
1. Sign up with a new account
2. Navigate to `/profile`
3. Verify that the profile loads without errors

### Step 4: Verify Database (Optional)
Run the test script to verify everything is working:

```sql
-- Execute this in your Supabase SQL editor
-- File: test-profile-setup.sql
```

## Changes Made

### Database Changes
1. **Fixed trigger function**: Now includes phone field in profile creation
2. **Added safeguards**: Prevents duplicate profile creation
3. **Updated existing profiles**: Ensures all profiles have phone field

### Code Changes
1. **Improved error handling**: Better logging and error messages
2. **Race condition prevention**: Added checks to prevent duplicate profile creation
3. **Specific column selection**: Replaced `select('*')` with specific columns
4. **Consistent queries**: All profile queries now use the same column selection

### Files Modified
- `src/hooks/useProfile.ts` - Improved error handling and race condition prevention
- `src/hooks/useProperties.ts` - Added phone field to profile queries
- `database-setup.sql` - Updated trigger function to include phone field
- `fix-profile-migration.sql` - New migration script to fix existing issues
- `test-profile-setup.sql` - New test script to verify setup

## Expected Behavior After Fix
1. New users signing up will automatically get a profile with phone field
2. Existing users navigating to `/profile` will see their profile data
3. Profile updates will work correctly
4. No more 406/400 HTTP errors

## Troubleshooting
If you still see issues:
1. Check browser console for specific error messages
2. Verify the migration script ran successfully
3. Check Supabase logs for any database errors
4. Ensure you're using the latest version of the code
