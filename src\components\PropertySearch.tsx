import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Search, Filter } from 'lucide-react';
import { SearchFilters, KENYAN_COUNTIES, COMMON_AMENITIES } from '@/types/property';

interface PropertySearchProps {
  onSearch: (filters: SearchFilters) => void;
  loading?: boolean;
  initialFilters?: SearchFilters;
}

export const PropertySearch: React.FC<PropertySearchProps> = ({
  onSearch,
  loading = false,
  initialFilters = {}
}) => {
  const [filters, setFilters] = useState<SearchFilters>(initialFilters);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [rentRange, setRentRange] = useState<[number, number]>([
    initialFilters.minRent || 0,
    initialFilters.maxRent || 200000
  ]);
  const [areaRange, setAreaRange] = useState<[number, number]>([
    initialFilters.minArea || 0,
    initialFilters.maxArea || 1000
  ]);

  const handleInputChange = (field: keyof SearchFilters, value: string | number | boolean) => {
    setFilters(prev => ({
      ...prev,
      [field]: value === '' ? undefined : value
    }));
  };

  const handleAmenityToggle = (amenity: string) => {
    setFilters(prev => {
      const currentAmenities = prev.amenities || [];
      const updatedAmenities = currentAmenities.includes(amenity)
        ? currentAmenities.filter(a => a !== amenity)
        : [...currentAmenities, amenity];
      
      return {
        ...prev,
        amenities: updatedAmenities.length > 0 ? updatedAmenities : undefined
      };
    });
  };

  const handleRentRangeChange = (values: number[]) => {
    setRentRange([values[0], values[1]]);
    setFilters(prev => ({
      ...prev,
      minRent: values[0] === 0 ? undefined : values[0],
      maxRent: values[1] === 200000 ? undefined : values[1]
    }));
  };

  const handleAreaRangeChange = (values: number[]) => {
    setAreaRange([values[0], values[1]]);
    setFilters(prev => ({
      ...prev,
      minArea: values[0] === 0 ? undefined : values[0],
      maxArea: values[1] === 1000 ? undefined : values[1]
    }));
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const clearFilters = () => {
    setFilters({});
    setRentRange([0, 200000]);
    setAreaRange([0, 1000]);
    onSearch({});
  };

  const activeFilterCount = Object.values(filters).filter(v => 
    v !== undefined && v !== '' && (Array.isArray(v) ? v.length > 0 : true)
  ).length;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Properties
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Advanced
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Search */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              placeholder="Enter location..."
              value={filters.location || ''}
              onChange={(e) => handleInputChange('location', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="county">County</Label>
            <Select
              value={filters.county || ''}
              onValueChange={(value) => handleInputChange('county', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select county" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Counties</SelectItem>
                {KENYAN_COUNTIES.map(county => (
                  <SelectItem key={county} value={county}>
                    {county}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bedrooms">Bedrooms</Label>
            <Select
              value={filters.bedrooms?.toString() || ''}
              onValueChange={(value) => handleInputChange('bedrooms', value ? parseInt(value) : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Any</SelectItem>
                {[1, 2, 3, 4, 5, 6].map(num => (
                  <SelectItem key={num} value={num.toString()}>
                    {num}+ Bedroom{num > 1 ? 's' : ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-6 border-t pt-4">
            {/* Rent Range */}
            <div className="space-y-3">
              <Label>Rent Range (KSh)</Label>
              <div className="px-3">
                <Slider
                  value={rentRange}
                  onValueChange={handleRentRangeChange}
                  max={200000}
                  min={0}
                  step={5000}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-1">
                  <span>KSh {rentRange[0].toLocaleString()}</span>
                  <span>KSh {rentRange[1].toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Area Range */}
            <div className="space-y-3">
              <Label>Area Range (sq. meters)</Label>
              <div className="px-3">
                <Slider
                  value={areaRange}
                  onValueChange={handleAreaRangeChange}
                  max={1000}
                  min={0}
                  step={10}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-1">
                  <span>{areaRange[0]} sqm</span>
                  <span>{areaRange[1]} sqm</span>
                </div>
              </div>
            </div>

            {/* Bathrooms */}
            <div className="space-y-2">
              <Label htmlFor="bathrooms">Bathrooms</Label>
              <Select
                value={filters.bathrooms?.toString() || ''}
                onValueChange={(value) => handleInputChange('bathrooms', value ? parseInt(value) : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  {[1, 2, 3, 4, 5].map(num => (
                    <SelectItem key={num} value={num.toString()}>
                      {num}+ Bathroom{num > 1 ? 's' : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Property Type Filters */}
            <div className="space-y-3">
              <Label>Property Type</Label>
              <div className="flex flex-wrap gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featured"
                    checked={filters.featured || false}
                    onCheckedChange={(checked) => 
                      handleInputChange('featured', checked === true)
                    }
                  />
                  <Label htmlFor="featured" className="text-sm">Featured Properties Only</Label>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div className="space-y-3">
              <Label>Amenities</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {COMMON_AMENITIES.map(amenity => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <Checkbox
                      id={`amenity-${amenity}`}
                      checked={filters.amenities?.includes(amenity) || false}
                      onCheckedChange={() => handleAmenityToggle(amenity)}
                    />
                    <Label 
                      htmlFor={`amenity-${amenity}`} 
                      className="text-sm cursor-pointer"
                    >
                      {amenity}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Amenities */}
            {filters.amenities && filters.amenities.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Amenities</Label>
                <div className="flex flex-wrap gap-2">
                  {filters.amenities.map(amenity => (
                    <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                      {amenity}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleAmenityToggle(amenity)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-4">
          <Button 
            onClick={handleSearch} 
            disabled={loading}
            className="flex-1"
          >
            {loading ? 'Searching...' : 'Search Properties'}
          </Button>
          
          {activeFilterCount > 0 && (
            <Button 
              variant="outline" 
              onClick={clearFilters}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Clear
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
