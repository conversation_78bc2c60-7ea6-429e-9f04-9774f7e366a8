# Database Setup Instructions

## Setting up the database with contact functionality

1. **Initial Setup**: Run the main database setup file
   ```sql
   -- Run this in your Supabase SQL editor
   \i database-setup.sql
   ```

2. **Add Phone Field**: If you already have the database set up, run the migration
   ```sql
   -- Run this in your Supabase SQL editor to add phone field
   \i add-phone-field-migration.sql
   ```

3. **Update Supabase Types**: After running the SQL, regenerate your types
   ```bash
   npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/integrations/supabase/types.ts
   ```

## Features Added

### 1. Contact Information in Signup
- Added phone number field to the signup form
- Phone number is now required during registration
- Phone number is stored in the user's metadata and profile

### 2. Profile Management Page
- New `/profile` route for users to manage their information
- Edit full name and phone number
- Phone number validation with proper format checking
- Profile page accessible from navigation menu

### 3. Contact Display in Property Details
- Landlord contact information displayed on property detail pages
- Click-to-call functionality for phone numbers
- Contact information pulled from landlord's profile

### 4. Navigation Updates
- Added "Profile" link in navigation menu
- Profile link appears when user is logged in

## Phone Number Format
The system accepts phone numbers in various formats:
- +254712345678
- 0712345678
- 254712345678
- (*************

## Security Notes
- Phone numbers are only visible to users viewing property details
- Users can edit their own contact information
- Contact information is essential for landlords listing properties
