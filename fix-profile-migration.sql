-- Comprehensive Profile Migration Fix
-- This script addresses profile creation, database schema, and RLS policy issues

-- Step 1: Drop existing RLS policies that might depend on user_id
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can delete own profile" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users based on user_id" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.profiles;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.profiles;

-- Step 2: Ensure profiles table has correct structure
DO $$ 
DECLARE
    deleted_count INTEGER;
BEGIN 
    -- Check if user_id column exists (some setups might have this instead of id as FK)
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'user_id'
        AND table_schema = 'public'
    ) THEN
        -- Drop user_id column if it exists (we use id as the primary key referencing auth.users)
        ALTER TABLE public.profiles DROP COLUMN user_id CASCADE;
        RAISE NOTICE 'Removed user_id column from profiles table';
    END IF;

    -- Ensure phone field exists in profiles table
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'phone'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.profiles ADD COLUMN phone TEXT;
        RAISE NOTICE 'Added phone column to profiles table';
    END IF;

    -- Ensure id column is properly set up as primary key referencing auth.users
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'profiles' 
        AND tc.constraint_type = 'PRIMARY KEY'
        AND kcu.column_name = 'id'
        AND tc.table_schema = 'public'
    ) THEN
        -- Add primary key constraint if missing
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);
        RAISE NOTICE 'Added primary key constraint to profiles.id';
    END IF;
    
    -- Clean up orphaned profiles before adding foreign key constraint
    DELETE FROM public.profiles 
    WHERE id NOT IN (SELECT id FROM auth.users);
    
    -- Get count of cleaned records
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    IF deleted_count > 0 THEN
        RAISE NOTICE 'Cleaned up % orphaned profile records', deleted_count;
    END IF;
    
    -- Ensure foreign key constraint to auth.users exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'profiles' 
        AND tc.constraint_type = 'FOREIGN KEY'
        AND kcu.column_name = 'id'
        AND tc.table_schema = 'public'
    ) THEN
        -- Add foreign key constraint if missing
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_id_fkey 
        FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint to profiles.id';
    END IF;
END $$;

-- Step 3: Update the trigger function to include phone field and handle errors
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, phone)
  VALUES (
    new.id, 
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    COALESCE(new.raw_user_meta_data->>'phone', '')
  )
  ON CONFLICT (id) DO UPDATE SET
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    phone = COALESCE(EXCLUDED.phone, profiles.phone),
    updated_at = NOW();
  
  RETURN new;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', new.id, SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Recreate the trigger (in case it was corrupted)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Step 5: Update existing profiles that might be missing phone field
UPDATE public.profiles 
SET phone = COALESCE(phone, '')
WHERE phone IS NULL;

-- Step 6: Create comprehensive RLS policies
CREATE POLICY "Enable read access for all users" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users based on user_id" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on user_id" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable delete for users based on user_id" ON public.profiles
  FOR DELETE USING (auth.uid() = id);

-- Step 7: Ensure RLS is enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Step 8: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_id ON public.profiles(id);

-- Step 9: Add comments for documentation
COMMENT ON TABLE public.profiles IS 'User profiles extending auth.users';
COMMENT ON COLUMN public.profiles.id IS 'References auth.users.id';
COMMENT ON COLUMN public.profiles.full_name IS 'User full name';
COMMENT ON COLUMN public.profiles.phone IS 'User phone number for contact purposes';

-- Step 10: Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Comprehensive profile migration completed successfully!';
    RAISE NOTICE 'Key changes:';
    RAISE NOTICE '- Fixed table structure (removed user_id, ensured id is PK)';
    RAISE NOTICE '- Updated trigger function with error handling';
    RAISE NOTICE '- Fixed RLS policies';
    RAISE NOTICE '- Added proper indexes and permissions';
END $$;
