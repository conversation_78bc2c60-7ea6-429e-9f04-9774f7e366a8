@echo off
echo Applying units column migration...
echo.
echo Please run the following SQL in your Supabase SQL Editor:
echo.
echo -- Add units column to properties table
echo ALTER TABLE public.properties 
echo ADD COLUMN IF NOT EXISTS units JSONB DEFAULT '[]'::jsonb;
echo.
echo -- Add a comment to document the column
echo COMMENT ON COLUMN public.properties.units IS 'JSON array containing unit data for multi-unit properties';
echo.
echo -- Optional: Add an index on the units column for better query performance
echo CREATE INDEX IF NOT EXISTS idx_properties_units ON public.properties USING GIN (units);
echo.
echo -- Update existing properties to have an empty units array if they don't have one
echo UPDATE public.properties 
echo SET units = '[]'::jsonb 
echo WHERE units IS NULL;
echo.
echo Migration ready to apply!
pause
